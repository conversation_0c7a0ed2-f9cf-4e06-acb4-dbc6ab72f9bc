@echo off
echo Testing frame capture and image saving...
echo.

cd /d "d:\Project\SuperBot\Client\build\Release"

echo Deleting old images...
rmdir /s /q images 2>nul

echo Starting client for 10 seconds to capture frames...
start /wait timeout /t 10 /nobreak
start SuperBotClient.exe

echo Waiting 15 seconds for client to capture frames...
timeout /t 15 /nobreak

echo Stopping client...
taskkill /f /im SuperBotClient.exe 2>nul

echo.
echo Checking for captured images...
if exist images (
    echo Images directory found!
    dir images
    echo.
    echo Frame capture test completed successfully!
    echo Check the images above to verify frame capture is working.
) else (
    echo No images directory found.
    echo Frame capture may not be working properly.
    echo Check the log file for errors.
)

echo.
echo Log file contents:
if exist SuperBotClient.log (
    type SuperBotClient.log | findstr /i "saving\|saved\|frame"
) else (
    echo No log file found.
)

echo.
pause
