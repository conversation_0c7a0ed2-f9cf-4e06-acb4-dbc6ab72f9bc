#include "Common.h"
#include <iostream>

int main() {
    std::string logPath = getLogFilePath();
    std::cout << "Log file path: " << logPath << std::endl;
    
    // Test if we can create the file
    FILE* testFile = nullptr;
    errno_t err = fopen_s(&testFile, logPath.c_str(), "w");
    if (err == 0 && testFile) {
        std::cout << "Successfully created test log file" << std::endl;
        fprintf(testFile, "Test log entry\n");
        fclose(testFile);
    } else {
        std::cout << "Failed to create test log file, error: " << err << std::endl;
    }
    
    return 0;
}
