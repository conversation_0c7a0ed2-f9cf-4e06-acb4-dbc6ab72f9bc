#include "Application.h"
#include "Logger.h"
#include <iostream>
#include <csignal>
#include <shlobj.h>
#include <string>

// Service name and display name
#define SERVICE_NAME L"SuperBotClient"
#define SERVICE_DISPLAY_NAME L"SuperBot Remote Desktop Client"
#define SERVICE_DESCRIPTION L"Provides remote desktop access for SuperBot connections"

// Global variables
Application* g_application = nullptr;
SERVICE_STATUS g_serviceStatus = {};
SERVICE_STATUS_HANDLE g_serviceStatusHandle = 0;
HANDLE g_serviceStopEvent = INVALID_HANDLE_VALUE;

// Forward declarations
void WINAPI ServiceMain(DWORD argc, LPWSTR* argv);
void WINAPI ServiceCtrlHandler(DWORD ctrl);
DWORD WINAPI ServiceWorkerThread(LPVOID lpParam);
void ReportServiceStatus(DWORD currentState, DWORD win32ExitCode, DWORD waitHint);
void InstallService();
void UninstallService();
void RunAsConsole(int argc, char* argv[]);

// Signal handlers
BOOL WINAPI ConsoleCtrlHandler(DWORD ctrlType)
{
    switch (ctrlType) {
    case CTRL_C_EVENT:
    case CTRL_BREAK_EVENT:
    case CTRL_CLOSE_EVENT:
    case CTRL_SHUTDOWN_EVENT:
        if (g_application) {
            g_application->requestShutdown();
        }
        return TRUE;
    default:
        return FALSE;
    }
}

void setupSignalHandlers()
{
    std::signal(SIGINT, [](int) {
        if (g_application) {
            g_application->requestShutdown();
        }
    });

    std::signal(SIGTERM, [](int) {
        if (g_application) {
            g_application->requestShutdown();
        }
    });

    SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);
}

void printBanner()
{
    std::cout << R"(
   ____                       ____        _     ______ _ _            _
  / ___| _   _ _ __   ___ _ __| __ )  ___ | |_  / ___| (_) ___ _ __ | |_
  \___ \| | | | '_ \ / _ \ '__|  _ \ / _ \| __|| |   | | |/ _ \ '_ \| __|
   ___) | |_| | |_) |  __/ |  | |_) | (_) | |_ | |___| | |  __/ | | | |_
  |____/ \__,_| .__/ \___|_|  |____/ \___/ \__| \____|_|_|\___|_| |_|\__|
              |_|
)" << std::endl;

    std::cout << "SuperBot Remote Desktop Client v" << SUPERBOT_VERSION_STRING << std::endl;
    std::cout << "High-performance remote desktop solution (Windows Service)" << std::endl;
    std::cout << "Copyright (c) 2024 SuperBot Team" << std::endl;
    std::cout << std::endl;
}

void printUsage()
{
    std::cout << "Usage: SuperBotClient [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -h, --help              Show this help message" << std::endl;
    std::cout << "  -v, --version           Show version information" << std::endl;
    std::cout << "  -p, --port <port>       Listen port (default: 7878)" << std::endl;
    std::cout << "  -c, --config <file>     Configuration file path" << std::endl;
    std::cout << "  -l, --log-level <level> Log level (debug, info, warning, error)" << std::endl;
    std::cout << "  --console               Run in console mode (for debugging)" << std::endl;
    std::cout << "  --install               Install as Windows service" << std::endl;
    std::cout << "  --uninstall             Uninstall Windows service" << std::endl;
    std::cout << "  --cert <file>           SSL certificate file" << std::endl;
    std::cout << "  --key <file>            SSL private key file" << std::endl;
    std::cout << "  --no-input              Disable remote input control" << std::endl;
    std::cout << std::endl;
}

void setupLogFileRedirection()
{
    // Get appropriate log file path
    std::string logFilePath = getLogFilePath();

    // Check command line to see if we're running in console mode
    LPWSTR cmdLine = GetCommandLineW();
    bool runAsConsole = false;
    if (cmdLine && wcsstr(cmdLine, L"--console")) {
        runAsConsole = true;
    }

    // Don't redirect if running in console mode
    if (runAsConsole) {
        return;
    }

    // Try to redirect stdout and stderr to log file
    FILE* logFile = nullptr;
    errno_t err = freopen_s(&logFile, logFilePath.c_str(), "w", stdout);
    if (err != 0) {
        // If redirection fails, continue without it
        // Don't show message box as it might not be visible in service mode
    }

    err = freopen_s(&logFile, logFilePath.c_str(), "a", stderr);
    if (err != 0) {
        // If redirection fails, continue without it
        // Don't show message box as it might not be visible in service mode
    }
}

int main(int argc, char* argv[])
{
    (void)argc;  // Suppress unused parameter warning
    (void)argv;  // Suppress unused parameter warning

    // Setup log file redirection with better error handling
    setupLogFileRedirection();

    // Initialize Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "Failed to initialize Winsock" << std::endl;
        return 1;
    }

    // Parse command line arguments
    bool runAsConsole = false;
    bool installService = false;
    bool uninstallService = false;
    bool showHelp = false;
    bool showVersion = false;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--console") {
            runAsConsole = true;
        } else if (arg == "--install") {
            installService = true;
        } else if (arg == "--uninstall") {
            uninstallService = true;
        } else if (arg == "-h" || arg == "--help") {
            showHelp = true;
        } else if (arg == "-v" || arg == "--version") {
            showVersion = true;
        }
    }

    if (showHelp) {
        printBanner();
        printUsage();
        WSACleanup();
        return 0;
    }

    if (showVersion) {
        std::cout << "SuperBot Client v" << SUPERBOT_VERSION_STRING << std::endl;
        WSACleanup();
        return 0;
    }

    if (installService) {
        InstallService();
        WSACleanup();
        return 0;
    }

    if (uninstallService) {
        UninstallService();
        WSACleanup();
        return 0;
    }

    if (runAsConsole) {
        // Allocate console for debugging
        AllocConsole();
        freopen("CONOUT$", "w", stdout);
        freopen("CONOUT$", "w", stderr);

        printBanner();
        RunAsConsole(argc, argv);

        FreeConsole();
        WSACleanup();
        return 0;
    }

    // Run as Windows service
    SERVICE_TABLE_ENTRY serviceTable[] = {
        { const_cast<LPWSTR>(SERVICE_NAME), ServiceMain },
        { nullptr, nullptr }
    };

    if (!StartServiceCtrlDispatcher(serviceTable)) {
        DWORD error = GetLastError();
        if (error == ERROR_FAILED_SERVICE_CONTROLLER_CONNECT) {
            // Not running as service, run in console mode
            std::cerr << "Not running as service. Use --console for console mode or --install to install service." << std::endl;
        } else {
            std::cerr << "StartServiceCtrlDispatcher failed: " << error << std::endl;
        }
        WSACleanup();
        return 1;
    }

    WSACleanup();
    return 0;
}

// Service implementation functions
void WINAPI ServiceMain(DWORD argc, LPWSTR* argv)
{
    // Register service control handler
    g_serviceStatusHandle = RegisterServiceCtrlHandler(SERVICE_NAME, ServiceCtrlHandler);
    if (!g_serviceStatusHandle) {
        return;
    }

    // Initialize service status
    g_serviceStatus.dwServiceType = SERVICE_WIN32_OWN_PROCESS;
    g_serviceStatus.dwCurrentState = SERVICE_START_PENDING;
    g_serviceStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP | SERVICE_ACCEPT_SHUTDOWN;
    g_serviceStatus.dwWin32ExitCode = 0;
    g_serviceStatus.dwServiceSpecificExitCode = 0;
    g_serviceStatus.dwCheckPoint = 0;

    // Report initial status
    ReportServiceStatus(SERVICE_START_PENDING, NO_ERROR, 3000);

    // Create stop event
    g_serviceStopEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
    if (!g_serviceStopEvent) {
        ReportServiceStatus(SERVICE_STOPPED, GetLastError(), 0);
        return;
    }

    // Report running status
    ReportServiceStatus(SERVICE_RUNNING, NO_ERROR, 0);

    // Start worker thread
    HANDLE hThread = CreateThread(nullptr, 0, ServiceWorkerThread, nullptr, 0, nullptr);
    if (hThread) {
        WaitForSingleObject(hThread, INFINITE);
        CloseHandle(hThread);
    }

    // Cleanup
    CloseHandle(g_serviceStopEvent);
    ReportServiceStatus(SERVICE_STOPPED, NO_ERROR, 0);
}

void WINAPI ServiceCtrlHandler(DWORD ctrl)
{
    switch (ctrl) {
    case SERVICE_CONTROL_STOP:
    case SERVICE_CONTROL_SHUTDOWN:
        ReportServiceStatus(SERVICE_STOP_PENDING, NO_ERROR, 0);
        SetEvent(g_serviceStopEvent);
        if (g_application) {
            g_application->requestShutdown();
        }
        break;
    default:
        break;
    }
}

DWORD WINAPI ServiceWorkerThread(LPVOID lpParam)
{
    // Create application instance
    const char* argv[] = { "SuperBotClient.exe" };
    g_application = new Application(1, const_cast<char**>(argv));

    try {
        // Initialize application
        if (!g_application->initialize()) {
            return 1;
        }

        // Run application
        int result = g_application->run();

        // Cleanup
        g_application->shutdown();
        delete g_application;
        g_application = nullptr;

        return result;
    }
    catch (const std::exception& e) {
        // Log error and exit
        delete g_application;
        g_application = nullptr;
        return 1;
    }
}

void ReportServiceStatus(DWORD currentState, DWORD win32ExitCode, DWORD waitHint)
{
    static DWORD checkPoint = 1;

    g_serviceStatus.dwCurrentState = currentState;
    g_serviceStatus.dwWin32ExitCode = win32ExitCode;
    g_serviceStatus.dwWaitHint = waitHint;

    if (currentState == SERVICE_START_PENDING) {
        g_serviceStatus.dwControlsAccepted = 0;
    } else {
        g_serviceStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP | SERVICE_ACCEPT_SHUTDOWN;
    }

    if ((currentState == SERVICE_RUNNING) || (currentState == SERVICE_STOPPED)) {
        g_serviceStatus.dwCheckPoint = 0;
    } else {
        g_serviceStatus.dwCheckPoint = checkPoint++;
    }

    SetServiceStatus(g_serviceStatusHandle, &g_serviceStatus);
}

void InstallService()
{
    SC_HANDLE schSCManager = OpenSCManager(nullptr, nullptr, SC_MANAGER_CREATE_SERVICE);
    if (!schSCManager) {
        std::cerr << "OpenSCManager failed: " << GetLastError() << std::endl;
        return;
    }

    // Get current executable path
    wchar_t szPath[MAX_PATH];
    if (!GetModuleFileName(nullptr, szPath, MAX_PATH)) {
        std::cerr << "GetModuleFileName failed: " << GetLastError() << std::endl;
        CloseServiceHandle(schSCManager);
        return;
    }

    SC_HANDLE schService = CreateService(
        schSCManager,
        SERVICE_NAME,
        SERVICE_DISPLAY_NAME,
        SERVICE_ALL_ACCESS,
        SERVICE_WIN32_OWN_PROCESS,
        SERVICE_AUTO_START,
        SERVICE_ERROR_NORMAL,
        szPath,
        nullptr,
        nullptr,
        nullptr,
        nullptr,
        nullptr
    );

    if (schService) {
        // Set service description
        SERVICE_DESCRIPTIONW sd = {};
        sd.lpDescription = const_cast<LPWSTR>(SERVICE_DESCRIPTION);
        ChangeServiceConfig2W(schService, SERVICE_CONFIG_DESCRIPTION, &sd);

        std::wcout << L"Service installed successfully." << std::endl;
        CloseServiceHandle(schService);
    } else {
        DWORD error = GetLastError();
        if (error == ERROR_SERVICE_EXISTS) {
            std::wcout << L"Service already exists." << std::endl;
        } else {
            std::wcerr << L"CreateService failed: " << error << std::endl;
        }
    }

    CloseServiceHandle(schSCManager);
}

void UninstallService()
{
    SC_HANDLE schSCManager = OpenSCManager(nullptr, nullptr, SC_MANAGER_CONNECT);
    if (!schSCManager) {
        std::cerr << "OpenSCManager failed: " << GetLastError() << std::endl;
        return;
    }

    SC_HANDLE schService = OpenService(schSCManager, SERVICE_NAME, DELETE);
    if (schService) {
        if (DeleteService(schService)) {
            std::wcout << L"Service uninstalled successfully." << std::endl;
        } else {
            std::wcerr << L"DeleteService failed: " << GetLastError() << std::endl;
        }
        CloseServiceHandle(schService);
    } else {
        std::wcerr << L"OpenService failed: " << GetLastError() << std::endl;
    }

    CloseServiceHandle(schSCManager);
}

void RunAsConsole(int argc, char* argv[])
{
    setupSignalHandlers();

    try {
        // Create application instance
        g_application = new Application(argc, argv);

        // Initialize application
        if (!g_application->initialize()) {
            std::cerr << "Failed to initialize application" << std::endl;
            delete g_application;
            return;
        }

        std::cout << "SuperBot Client started in console mode" << std::endl;
        std::cout << "Listening on port " << g_application->getPort() << std::endl;
        std::cout << "Press Ctrl+C to stop..." << std::endl;

        // Run application
        int result = g_application->run();

        std::cout << "SuperBot Client shutting down" << std::endl;

        // Cleanup
        g_application->shutdown();
        delete g_application;
        g_application = nullptr;
    }
    catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        if (g_application) {
            delete g_application;
            g_application = nullptr;
        }
    }
}
