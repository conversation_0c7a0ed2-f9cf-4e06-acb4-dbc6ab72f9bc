@echo off
echo Building client...
cd /d "d:\Project\SuperBot\Client\build"
cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.
echo Deleting old log file and images...
del Release\SuperBotClient.log 2>nul
rmdir /s /q Release\images 2>nul

echo.
echo Client built successfully!
echo Run the client to test frame capture and image saving.
echo First 5 frames will be saved to images/ directory as BMP files.
echo.
pause
