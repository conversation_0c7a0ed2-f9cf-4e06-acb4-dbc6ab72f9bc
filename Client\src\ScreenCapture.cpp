#include "ScreenCapture.h"
#include "Logger.h"
#include "Compression.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>

// Windows-specific data structure
struct ScreenCapture::WindowsData {
    HDC desktopDC;
    HDC memoryDC;
    HBITMAP bitmap;
    BITMAPINFO bitmapInfo;
    int screenWidth;
    int screenHeight;
    std::vector<uint8_t> bitmapData;
};

ScreenCapture::ScreenCapture()
    : m_capturing(false)
    , m_frameRate(DEFAULT_FRAME_RATE)
    , m_quality(DEFAULT_SCREEN_QUALITY)
    , m_format(ScreenFormat::RGB32)
    , m_compressionEnabled(true)
    , m_deltaFramesEnabled(true)
    , m_adaptiveQuality(true)
    , m_hardwareAcceleration(false)
    , m_activeMonitor(0)
    , m_useCustomRegion(false)
    , m_maxFrameSize(MAX_FRAME_SIZE)
    , m_targetFrameRate(DEFAULT_FRAME_RATE)
    , m_currentFrameRate(0)
    , m_averageFrameSize(0)
    , m_cpuUsage(0.0)
    , m_frameCount(0)
    , m_totalFrameSize(0)
    , m_windowsData(std::make_unique<WindowsData>())
{
    m_lastCaptureTime = std::chrono::steady_clock::now();
    m_lastStatsUpdate = std::chrono::steady_clock::now();
    m_captureStartTime = std::chrono::steady_clock::now();

    InitializeCriticalSection(&m_captureCriticalSection);

    // Initialize compression manager
    m_compressionManager = std::make_unique<Compression::CompressionManager>();
    m_compressionManager->setPreferredCompression(Compression::CompressionType::LZ4);
}

ScreenCapture::~ScreenCapture()
{
    shutdown();
    DeleteCriticalSection(&m_captureCriticalSection);
}

bool ScreenCapture::initialize()
{
    LOG_INFO("Initializing screen capture");

    if (!initializePlatformCapture()) {
        LOG_ERROR("Failed to initialize platform-specific screen capture");
        return false;
    }

    detectMonitors();
    LOG_INFO("Screen capture initialized successfully");
    return true;
}

void ScreenCapture::shutdown()
{
    LOG_INFO("Shutting down screen capture");
    stop();
    cleanupPlatformCapture();
}

bool ScreenCapture::start()
{
    LOG_INFO("Starting screen capture");
    m_capturing = true;
    m_captureStartTime = std::chrono::steady_clock::now();
    return true;
}

void ScreenCapture::stop()
{
    LOG_INFO("Stopping screen capture");
    m_capturing = false;
}

void ScreenCapture::processFrame()
{
    if (!m_capturing) {
        LOG_DEBUG("processFrame called but not capturing");
        return;
    }

    LOG_DEBUG("processFrame called - capturing active");

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastCapture = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastCaptureTime);

    // Check if it's time for the next frame
    int frameInterval = 1000 / m_frameRate;
    if (timeSinceLastCapture.count() < frameInterval) {
        return;
    }

    m_lastCaptureTime = now;

    try {
        ScreenFrame frame = captureScreen();

        if (shouldSendFrame(frame)) {
            m_frameCount++;
            m_totalFrameSize += static_cast<int>(frame.data.size());

            LOG_DEBUG("Screen frame captured: " + std::to_string(frame.region.width) + "x" +
                     std::to_string(frame.region.height) + ", data size: " + std::to_string(frame.data.size()));

            // Debug: Verify frame region before callback
            LOG_INFO("Before callback - frame.region: x=" + std::to_string(frame.region.x) +
                     ", y=" + std::to_string(frame.region.y) +
                     ", width=" + std::to_string(frame.region.width) +
                     ", height=" + std::to_string(frame.region.height));

            if (m_frameReadyCallback) {
                LOG_DEBUG("Calling frame ready callback");
                m_frameReadyCallback(frame);
                LOG_DEBUG("Frame ready callback completed");
            } else {
                LOG_WARNING("No frame ready callback set");
            }
        } else {
            LOG_DEBUG("Frame not sent (shouldSendFrame returned false)");
        }

        // Update statistics periodically
        auto timeSinceStats = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastStatsUpdate);
        if (timeSinceStats.count() >= STATS_UPDATE_INTERVAL) {
            updatePerformanceStats();
            m_lastStatsUpdate = now;
        }
    }
    catch (const std::exception& e) {
        LOG_ERROR("Screen capture error: " + std::string(e.what()));
        if (m_errorCallback) {
            m_errorCallback(e.what());
        }
    }
}

std::vector<MonitorInfo> ScreenCapture::getMonitors() const
{
    return m_monitors;
}

void ScreenCapture::setCaptureRegion(const Rect& region)
{
    EnterCriticalSection(&m_captureCriticalSection);
    m_captureRegion = region;
    m_useCustomRegion = true;
    LeaveCriticalSection(&m_captureCriticalSection);
}

void ScreenCapture::resetCaptureRegion()
{
    EnterCriticalSection(&m_captureCriticalSection);
    m_useCustomRegion = false;
    LeaveCriticalSection(&m_captureCriticalSection);
}

void ScreenCapture::setFrameReadyCallback(std::function<void(const ScreenFrame&)> callback)
{
    m_frameReadyCallback = callback;
}

void ScreenCapture::setErrorCallback(std::function<void(const std::string&)> callback)
{
    m_errorCallback = callback;
}

bool ScreenCapture::initializePlatformCapture()
{
    // Initialize Windows GDI capture
    m_windowsData->desktopDC = GetDC(nullptr);
    if (!m_windowsData->desktopDC) {
        LOG_ERROR("Failed to get desktop DC");
        return false;
    }

    m_windowsData->memoryDC = CreateCompatibleDC(m_windowsData->desktopDC);
    if (!m_windowsData->memoryDC) {
        LOG_ERROR("Failed to create compatible DC");
        return false;
    }

    // Get screen dimensions using GetSystemMetrics
    m_windowsData->screenWidth = GetSystemMetrics(SM_CXSCREEN);
    m_windowsData->screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // Validate screen dimensions
    if (m_windowsData->screenWidth <= 0 || m_windowsData->screenHeight <= 0) {
        LOG_ERROR("Invalid screen dimensions: " + std::to_string(m_windowsData->screenWidth) +
                 "x" + std::to_string(m_windowsData->screenHeight));
        return false;
    }

    LOG_INFO("Detected screen dimensions: " + std::to_string(m_windowsData->screenWidth) +
             "x" + std::to_string(m_windowsData->screenHeight));

    // Create bitmap with actual screen dimensions
    m_windowsData->bitmap = CreateCompatibleBitmap(m_windowsData->desktopDC,
        m_windowsData->screenWidth, m_windowsData->screenHeight);
    if (!m_windowsData->bitmap) {
        LOG_ERROR("Failed to create compatible bitmap");
        return false;
    }

    SelectObject(m_windowsData->memoryDC, m_windowsData->bitmap);

    // Setup bitmap info
    ZeroMemory(&m_windowsData->bitmapInfo, sizeof(BITMAPINFO));
    m_windowsData->bitmapInfo.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    m_windowsData->bitmapInfo.bmiHeader.biWidth = m_windowsData->screenWidth;
    m_windowsData->bitmapInfo.bmiHeader.biHeight = -m_windowsData->screenHeight; // Top-down
    m_windowsData->bitmapInfo.bmiHeader.biPlanes = 1;
    m_windowsData->bitmapInfo.bmiHeader.biBitCount = 32;
    m_windowsData->bitmapInfo.bmiHeader.biCompression = BI_RGB;

    // Allocate bitmap data buffer
    size_t dataSize = m_windowsData->screenWidth * m_windowsData->screenHeight * 4;
    m_windowsData->bitmapData.resize(dataSize);

    LOG_INFO("Initialized screen capture with " + std::to_string(dataSize) + " bytes buffer");
    return true;
}

void ScreenCapture::cleanupPlatformCapture()
{
    if (m_windowsData->bitmap) {
        DeleteObject(m_windowsData->bitmap);
        m_windowsData->bitmap = nullptr;
    }

    if (m_windowsData->memoryDC) {
        DeleteDC(m_windowsData->memoryDC);
        m_windowsData->memoryDC = nullptr;
    }

    if (m_windowsData->desktopDC) {
        ReleaseDC(nullptr, m_windowsData->desktopDC);
        m_windowsData->desktopDC = nullptr;
    }
}

ScreenFrame ScreenCapture::captureScreen()
{
    EnterCriticalSection(&m_captureCriticalSection);
    ScreenFrame result;
    if (m_useCustomRegion) {
        result = captureRegion(m_captureRegion);
    } else {
        result = captureMonitor(m_activeMonitor);
    }

    LeaveCriticalSection(&m_captureCriticalSection);
    return result;
}

ScreenFrame ScreenCapture::captureMonitor(int monitorId)
{
    ScreenFrame frame;
    frame.monitorId = monitorId;

    // Debug: Log screen dimensions
    LOG_DEBUG("Screen dimensions: " + std::to_string(m_windowsData->screenWidth) + "x" + std::to_string(m_windowsData->screenHeight));

    // Validate screen dimensions
    if (m_windowsData->screenWidth <= 0 || m_windowsData->screenHeight <= 0) {
        LOG_ERROR("Invalid screen dimensions: " + std::to_string(m_windowsData->screenWidth) + "x" + std::to_string(m_windowsData->screenHeight));
        return frame;
    }

    frame.region = Rect(0, 0, m_windowsData->screenWidth, m_windowsData->screenHeight);
    frame.format = ScreenFormat::BGR24; // Change to BGR24 to match server's expected format
    frame.isFullFrame = true;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();

    LOG_DEBUG("Frame region set to: " + std::to_string(frame.region.width) + "x" + std::to_string(frame.region.height));

    // Capture screen using BitBlt
    if (!BitBlt(m_windowsData->memoryDC, 0, 0,
        m_windowsData->screenWidth, m_windowsData->screenHeight,
        m_windowsData->desktopDC, 0, 0, SRCCOPY)) {
        LOG_ERROR("BitBlt failed: " + std::to_string(GetLastError()));
        return frame;
    }

    // Get bitmap data
    if (GetDIBits(m_windowsData->desktopDC, m_windowsData->bitmap, 0,
        m_windowsData->screenHeight, m_windowsData->bitmapData.data(),
        &m_windowsData->bitmapInfo, DIB_RGB_COLORS) == 0) {
        LOG_ERROR("GetDIBits failed: " + std::to_string(GetLastError()));
        return frame;
    }

    // Process frame data - keep BGR format instead of converting to RGB
    frame.data = convertBGRAtoBGR24(m_windowsData->bitmapData, m_windowsData->screenWidth, m_windowsData->screenHeight);

    LOG_DEBUG("Converted frame data from " + std::to_string(m_windowsData->bitmapData.size()) +
              " bytes (BGRA32) to " + std::to_string(frame.data.size()) + " bytes (BGR24)");

    // Save first few frames for debugging (only save first 5 frames to avoid too many files)
    static int frameCounter = 0;
    if (frameCounter < 5) {
        frameCounter++;

        // Generate filename with timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << "frame_" << frameCounter << "_"
           << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
           << "_" << std::setfill('0') << std::setw(3) << ms.count();

        std::string filename = ss.str();

        LOG_INFO("Saving frame " + std::to_string(frameCounter) + " as: " + filename);

        // Save the frame before compression
        if (saveFrameAsJPEG(frame.data, m_windowsData->screenWidth, m_windowsData->screenHeight, filename)) {
            LOG_INFO("Successfully saved frame " + std::to_string(frameCounter));
        } else {
            LOG_ERROR("Failed to save frame " + std::to_string(frameCounter));
        }
    }

    // Apply compression if enabled
    if (m_compressionEnabled) {
        auto originalSize = frame.data.size();
        frame.data = compressFrame(frame.data, m_format);
        frame.isCompressed = (frame.data.size() < originalSize);

        if (frame.isCompressed) {
            LOG_DEBUG("Frame compressed from " + std::to_string(originalSize) +
                     " to " + std::to_string(frame.data.size()) + " bytes");
        } else {
            LOG_DEBUG("Frame not compressed, keeping original size: " + std::to_string(frame.data.size()) + " bytes");
        }
    }

    return frame;
}

ScreenFrame ScreenCapture::captureRegion(const Rect& region)
{
    // For simplicity, capture full screen and crop
    // In a real implementation, you would optimize this
    ScreenFrame fullFrame = captureMonitor(m_activeMonitor);

    // TODO: Implement region cropping
    fullFrame.region = region;
    return fullFrame;
}

ScreenFrame ScreenCapture::processFrame(const std::vector<uint8_t>& rawData, const Size& size)
{
    (void)size; // Suppress unused parameter warning
    ScreenFrame frame;
    frame.data = rawData;
    // TODO: Process frame data
    return frame;
}

std::vector<uint8_t> ScreenCapture::compressFrame(const std::vector<uint8_t>& data, ScreenFormat format)
{
    (void)format; // Suppress unused parameter warning
    if (!m_compressionManager || data.empty()) {
        return data;
    }

    try {
        auto result = m_compressionManager->compressScreenData(data);

        if (result.success && result.ratio < 0.9) { // Only use compression if it saves at least 10%
            LOG_DEBUG("Compressed frame from " + std::to_string(result.originalSize) +
                     " to " + std::to_string(result.compressedSize) +
                     " bytes (ratio: " + std::to_string(result.ratio) + ")");
            return result.data;
        } else {
            LOG_DEBUG("Compression not beneficial, using original data");
            return data;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Compression failed: " + std::string(e.what()));
        return data;
    }
}

ScreenFrame ScreenCapture::createDeltaFrame(const ScreenFrame& current, const ScreenFrame& previous)
{
    (void)previous; // Suppress unused parameter warning
    // TODO: Implement delta frame creation
    return current;
}

bool ScreenCapture::shouldSendFrame(const ScreenFrame& frame)
{
    (void)frame; // Suppress unused parameter warning
    // TODO: Implement frame filtering logic
    return true;
}

void ScreenCapture::adjustQuality()
{
    // TODO: Implement adaptive quality adjustment
}

int ScreenCapture::calculateOptimalQuality(int frameSize, int targetSize)
{
    (void)frameSize; // Suppress unused parameter warning
    (void)targetSize; // Suppress unused parameter warning
    // TODO: Implement quality calculation
    return m_quality;
}

void ScreenCapture::updateFrameRateStats()
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_captureStartTime);

    if (elapsed.count() > 0) {
        m_currentFrameRate = m_frameCount / static_cast<int>(elapsed.count());
    }
}

void ScreenCapture::detectMonitors()
{
    m_monitors.clear();

    // Get primary monitor info
    MonitorInfo primaryMonitor;
    primaryMonitor.id = 0;
    primaryMonitor.bounds = Rect(0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN));
    primaryMonitor.primary = true;
    primaryMonitor.refreshRate = 60; // Default
    primaryMonitor.name = "Primary Monitor";

    m_monitors.push_back(primaryMonitor);

    // Get all monitors using EnumDisplayMonitors
    EnumDisplayMonitors(nullptr, nullptr, [](HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) -> BOOL {
        auto* monitors = reinterpret_cast<std::vector<MonitorInfo>*>(dwData);

        MONITORINFOEX monitorInfo;
        monitorInfo.cbSize = sizeof(MONITORINFOEX);
        if (GetMonitorInfo(hMonitor, &monitorInfo)) {
            MonitorInfo monitor;
            monitor.id = static_cast<int>(monitors->size());
            monitor.bounds = Rect(
                monitorInfo.rcMonitor.left,
                monitorInfo.rcMonitor.top,
                monitorInfo.rcMonitor.right - monitorInfo.rcMonitor.left,
                monitorInfo.rcMonitor.bottom - monitorInfo.rcMonitor.top
            );
            monitor.primary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
            monitor.refreshRate = 60; // Default

            // Convert WCHAR to std::string
            char deviceName[32];
            WideCharToMultiByte(CP_UTF8, 0, monitorInfo.szDevice, -1, deviceName, sizeof(deviceName), nullptr, nullptr);
            monitor.name = deviceName;

            monitors->push_back(monitor);
        }
        return TRUE;
    }, reinterpret_cast<LPARAM>(&m_monitors));

    LOG_INFO("Detected " + std::to_string(m_monitors.size()) + " monitor(s)");
    for (const auto& monitor : m_monitors) {
        LOG_INFO("Monitor " + std::to_string(monitor.id) + ": " +
                 std::to_string(monitor.bounds.width) + "x" + std::to_string(monitor.bounds.height) +
                 (monitor.primary ? " (Primary)" : ""));
    }
}

void ScreenCapture::updateMonitorInfo()
{
    // TODO: Update monitor information
}

void ScreenCapture::updatePerformanceStats()
{
    updateFrameRateStats();

    if (m_frameCount > 0) {
        m_averageFrameSize = m_totalFrameSize / m_frameCount;
    }

    m_cpuUsage = calculateCpuUsage();
}

double ScreenCapture::calculateCpuUsage()
{
    // TODO: Implement CPU usage calculation
    return 0.0;
}

std::vector<uint8_t> ScreenCapture::convertBGRAtoRGB24(const std::vector<uint8_t>& bgraData, int width, int height)
{
    // Validate input parameters
    if (bgraData.empty() || width <= 0 || height <= 0) {
        LOG_ERROR("Invalid parameters for BGRA to RGB24 conversion");
        return std::vector<uint8_t>();
    }

    // Calculate expected size
    size_t expectedSize = width * height * 4;
    if (bgraData.size() < expectedSize) {
        LOG_ERROR("BGRA data size mismatch: expected " + std::to_string(expectedSize) +
                 " bytes, got " + std::to_string(bgraData.size()) + " bytes");
        return std::vector<uint8_t>();
    }
}

std::vector<uint8_t> ScreenCapture::convertBGRAtoBGR24(const std::vector<uint8_t>& bgraData, int width, int height)
{
    // Validate input parameters
    if (bgraData.empty() || width <= 0 || height <= 0) {
        LOG_ERROR("Invalid parameters for BGRA to BGR24 conversion");
        return std::vector<uint8_t>();
    }

    // Calculate expected size
    size_t expectedSize = width * height * 4;
    if (bgraData.size() < expectedSize) {
        LOG_ERROR("BGRA data size mismatch: expected " + std::to_string(expectedSize) +
                 " bytes, got " + std::to_string(bgraData.size()) + " bytes");
        return std::vector<uint8_t>();
    }

    // Convert BGRA32 (4 bytes per pixel) to BGR24 (3 bytes per pixel)
    std::vector<uint8_t> bgr24Data;
    bgr24Data.reserve(width * height * 3);

    // Convert BGRA to BGR24 (just remove alpha channel)
    for (int i = 0, j = 0; i < bgraData.size(); i += 4, j += 3) {
        // BGRA to BGR (keep BGR order, just remove A)
        bgr24Data.push_back(bgraData[i]);         // B (same)
        bgr24Data.push_back(bgraData[i + 1]);     // G (same)
        bgr24Data.push_back(bgraData[i + 2]);     // R (same)
        // Alpha is discarded
    }

    LOG_DEBUG("First 3 pixels BGR values: (" +
              std::to_string(bgr24Data[0]) + "," + std::to_string(bgr24Data[1]) + "," + std::to_string(bgr24Data[2]) + ") (" +
              std::to_string(bgr24Data[3]) + "," + std::to_string(bgr24Data[4]) + "," + std::to_string(bgr24Data[5]) + ") (" +
              std::to_string(bgr24Data[6]) + "," + std::to_string(bgr24Data[7]) + "," + std::to_string(bgr24Data[8]) + ")");

    return bgr24Data;
}

bool ScreenCapture::saveFrameAsJPEG(const std::vector<uint8_t>& frameData, int width, int height, const std::string& filename)
{
    try {
        // Create images directory if it doesn't exist
        std::filesystem::create_directories("images");

        std::string fullPath = "images/" + filename;

        // Create bitmap info for the frame data
        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = -height; // Top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 24; // BGR24
        bmi.bmiHeader.biCompression = BI_RGB;
        bmi.bmiHeader.biSizeImage = width * height * 3;

        // Create a device context
        HDC hdc = GetDC(nullptr);
        if (!hdc) {
            LOG_ERROR("Failed to get device context for JPEG save");
            return false;
        }

        // Create a bitmap from the frame data
        HBITMAP hBitmap = CreateDIBitmap(hdc, &bmi.bmiHeader, CBM_INIT,
                                        frameData.data(), &bmi, DIB_RGB_COLORS);
        if (!hBitmap) {
            LOG_ERROR("Failed to create bitmap for JPEG save");
            ReleaseDC(nullptr, hdc);
            return false;
        }

        // Save as BMP first (simpler approach)
        std::string bmpPath = fullPath + ".bmp";
        bool success = saveBitmapToFile(hBitmap, bmpPath);

        // Clean up
        DeleteObject(hBitmap);
        ReleaseDC(nullptr, hdc);

        if (success) {
            LOG_INFO("Frame saved as: " + bmpPath);
        } else {
            LOG_ERROR("Failed to save frame as: " + bmpPath);
        }

        return success;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Exception in saveFrameAsJPEG: " + std::string(e.what()));
        return false;
    }
}

bool ScreenCapture::saveBitmapToFile(HBITMAP hBitmap, const std::string& filename)
{
    BITMAP bmp;
    if (GetObject(hBitmap, sizeof(BITMAP), &bmp) == 0) {
        LOG_ERROR("Failed to get bitmap object info");
        return false;
    }

    BITMAPFILEHEADER bmfHeader;
    BITMAPINFOHEADER bi;

    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = bmp.bmWidth;
    bi.biHeight = bmp.bmHeight;
    bi.biPlanes = 1;
    bi.biBitCount = 24;
    bi.biCompression = BI_RGB;
    bi.biSizeImage = 0;
    bi.biXPelsPerMeter = 0;
    bi.biYPelsPerMeter = 0;
    bi.biClrUsed = 0;
    bi.biClrImportant = 0;

    DWORD dwBmpSize = ((bmp.bmWidth * bi.biBitCount + 31) / 32) * 4 * bmp.bmHeight;

    // Add the size of the headers to the size of the bitmap to get the total file size
    DWORD dwSizeofDIB = dwBmpSize + sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);

    // Offset to where the actual bitmap bits start.
    bmfHeader.bfOffBits = (DWORD)sizeof(BITMAPFILEHEADER) + (DWORD)sizeof(BITMAPINFOHEADER);

    // Size of the file
    bmfHeader.bfSize = dwSizeofDIB;

    // bfType must always be BM for Bitmaps
    bmfHeader.bfType = 0x4D42; // BM

    HDC hdc = GetDC(nullptr);
    std::vector<char> lpbitmap(dwBmpSize);

    // Gets the "bits" from the bitmap and copies them into a buffer
    // which is pointed to by lpbitmap.
    GetDIBits(hdc, hBitmap, 0, (UINT)bmp.bmHeight, lpbitmap.data(), (BITMAPINFO*)&bi, DIB_RGB_COLORS);

    // Create the .BMP file.
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        LOG_ERROR("Failed to create file: " + filename);
        ReleaseDC(nullptr, hdc);
        return false;
    }

    // Write the file header
    file.write((char*)&bmfHeader, sizeof(BITMAPFILEHEADER));

    // Write the bitmap info header
    file.write((char*)&bi, sizeof(BITMAPINFOHEADER));

    // Write the bitmap data
    file.write(lpbitmap.data(), dwBmpSize);

    file.close();
    ReleaseDC(nullptr, hdc);

    return true;
}
