@echo off
echo Building client...
cd /d "d:\Project\SuperBot\Client\build"
cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.
echo Deleting old log file and images...
del Release\SuperBotClient.log 2>nul
rmdir /s /q Release\images 2>nul

echo Starting client...
cd Release
start SuperBotClient.exe

echo Building server...
cd /d "d:\Project\SuperBot\Server\SuperBotServer"
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.
echo Starting server...
start dotnet run

echo.
echo Client will save first 5 captured frames to images/ directory
echo Check the images/ folder after running to verify frame capture
echo.
echo Done!
pause
