cmake_minimum_required(VERSION 3.20)
project(SuperBotClient VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows-only build (<PERSON><PERSON><PERSON> only)
if(NOT WIN32)
    message(FATAL_ERROR "This client is Windows-only and uses WinAPI")
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Source files
set(SOURCES
    src/main.cpp
    src/Application.cpp
    src/NetworkClient.cpp
    src/SecurityManager.cpp
    src/ScreenCapture.cpp
    src/InputHandler.cpp
    src/Logger.cpp
    src/Compression.cpp
    src/Utils.cpp
)

# Header files
set(HEADERS
    include/Common.h
    include/Application.h
    include/NetworkClient.h
    include/SecurityManager.h
    include/ScreenCapture.h
    include/InputHandler.h
    include/Logger.h
    include/Compression.h
)

# Create executable (Windows service)
add_executable(SuperBotClient ${SOURCES} ${HEADERS})

# Link Windows libraries only
target_link_libraries(SuperBotClient PRIVATE
    user32
    gdi32
    kernel32
    ws2_32
    advapi32
    crypt32
    shell32
    winmm
)

# Find and link LZ4
find_package(lz4 CONFIG REQUIRED)
target_link_libraries(SuperBotClient PRIVATE lz4::lz4)

# Compiler-specific options
if(MSVC)
    target_compile_options(SuperBotClient PRIVATE /W4)
    target_compile_definitions(SuperBotClient PRIVATE
        _WIN32_WINNT=0x0A00
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        UNICODE
        _UNICODE
        _CRT_SECURE_NO_WARNINGS
    )
else()
    target_compile_options(SuperBotClient PRIVATE -Wall -Wextra -pedantic)
    target_compile_definitions(SuperBotClient PRIVATE
        _WIN32_WINNT=0x0A00
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        UNICODE
        _UNICODE
        _CRT_SECURE_NO_WARNINGS
    )
endif()

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(SuperBotClient PRIVATE DEBUG_BUILD)
else()
    target_compile_definitions(SuperBotClient PRIVATE RELEASE_BUILD)
    if(NOT MSVC)
        target_compile_options(SuperBotClient PRIVATE -O3)
    endif()
endif()

# Installation
install(TARGETS SuperBotClient
    RUNTIME DESTINATION bin
)

# Package configuration
set(CPACK_PACKAGE_NAME "SuperBotClient")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SuperBot Remote Desktop Client Service")
set(CPACK_PACKAGE_VENDOR "SuperBot Team")
set(CPACK_GENERATOR "NSIS")

include(CPack)
